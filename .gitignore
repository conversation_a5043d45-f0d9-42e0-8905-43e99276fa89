# Python cache files
__pycache__/
*.py[cod]
*$py.class

# Environment files
.env
.env.local
.env.*.local

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Aider files
.aider*

# Byte-compiled / optimized / DLL files
*.pyc
*.pyo
*.pyd
*.so
*.dll

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/
