import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import logging
from sqlalchemy import func, and_
from database.config import Sessions
from database.models import SalesHeader

class SalesHistoryWindow:
    def __init__(self, parent, selected_date):
        self.parent = parent  # This will now be the App instance
        self.window = tk.Toplevel(parent.root)  # Use parent.root for the Toplevel window
        self.window.title("Sales History (14 Days)")
        self.window.geometry("600x400")
        
        # Create main frame
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.window.grid_rowconfigure(0, weight=1)
        self.window.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)  # Row with treeview expands
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Add label
        ttk.Label(main_frame, text="Sales History for Last 14 Days (Target DB)", 
                 font=('Helvetica', 12, 'bold')).grid(row=0, column=0, pady=(0, 10))
        
        # Create Treeview
        self.tree = ttk.Treeview(main_frame, columns=('date', 'count', 'total'), show='headings')
        self.tree.heading('date', text='Date')
        self.tree.heading('count', text='Number of Sales')
        self.tree.heading('total', text='Total Sales')
        
        # Configure column widths
        self.tree.column('date', width=200)
        self.tree.column('count', width=150)
        self.tree.column('total', width=200)
        
        self.tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.tree.yview)
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Bind double-click event
        self.tree.bind('<Double-1>', self.on_row_double_click)
        
        # Add close button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(10, 0))
        
        # Add close button
        ttk.Button(button_frame, text="Close", command=self.window.destroy).pack()
        
        # Load and display data
        self.load_sales_history(selected_date)

    def format_date(self, date):
        # Dictionary for day name translations
        day_names = {
            0: 'Mon', 1: 'Tue', 2: 'Wed', 
            3: 'Thu', 4: 'Fri', 5: 'Sat', 6: 'Sun'
        }
        day_name = day_names[date.weekday()]
        return f"{day_name}, {date.strftime('%d-%m-%Y')}"

    def load_sales_history(self, selected_date):
        try:
            session = Sessions["target"]()
            
            # Ensure selected_date is in correct format
            # If selected_date is a string, parse it correctly
            if isinstance(selected_date, str):
                selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            elif isinstance(selected_date, datetime):
                selected_date = selected_date.date()
            
            # Calculate date range
            end_date = selected_date
            start_date = end_date - timedelta(days=13)
            
            # Convert to datetime with time set to start and end of day
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            
            logging.info(f"Searching sales history from {start_datetime} to {end_datetime}")
            
            # Query sales data grouped by date
            sales_data = session.query(
                func.date_trunc('day', SalesHeader.tanggal).label('sale_date'),
                func.count(SalesHeader.notransaksi).label('sale_count'),
                func.sum(SalesHeader.totalakhir).label('sale_total')
            ).filter(
                and_(
                    SalesHeader.tanggal >= start_datetime,
                    SalesHeader.tanggal <= end_datetime,
                    SalesHeader.notransaksi.like('%/KSR/%')
                )
            ).group_by(func.date_trunc('day', SalesHeader.tanggal)
            ).order_by('sale_date').all()
            
            # Create a dictionary of existing sales dates
            sales_dict = {result.sale_date.date(): {
                'count': result.sale_count,
                'total': result.sale_total
            } for result in sales_data}
            
            # Clear existing items
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Iterate through all dates in range
            current_date = start_date
            while current_date <= end_date:
                # Format date with day name
                formatted_date = self.format_date(current_date)
                
                if current_date in sales_dict:
                    sale_info = sales_dict[current_date]
                    self.tree.insert('', 'end', values=(
                        formatted_date,
                        sale_info['count'],
                        f"Rp {sale_info['total']:,.2f}"
                    ), tags=('has_sales',))
                else:
                    self.tree.insert('', 'end', values=(
                        formatted_date,
                        'No sales',
                        'Rp 0.00'
                    ), tags=('no_sales',))
                
                current_date += timedelta(days=1)
            
            # Configure tag colors
            self.tree.tag_configure('has_sales', background='#E8F5E9')
            self.tree.tag_configure('no_sales', background='#FFEBEE')
            
        except Exception as e:
            logging.error(f"Error loading sales history: {str(e)}", exc_info=True)
            tk.messagebox.showerror("Error", f"Failed to load sales history: {str(e)}")
        finally:
            session.close()

    def on_row_double_click(self, event):
        # Get the selected item
        selection = self.tree.selection()
        if not selection:
            return
            
        # Get the date from the selected row
        item = self.tree.item(selection[0])
        date_str = item['values'][0]  # Get the date string from first column
        
        try:
            # Parse the date (format: "Day, DD-MM-YYYY")
            date_str = date_str.split(', ')[1]  # Get "DD-MM-YYYY" part
            selected_date = datetime.strptime(date_str, '%d-%m-%Y').date()
            
            # Update main window's date selector
            self.parent.date_selector.set_date(selected_date)
            
            # Close the history window
            self.window.destroy()
            
        except Exception as e:
            logging.error(f"Error parsing date: {str(e)}", exc_info=True)

def show_sales_history(parent, selected_date):
    SalesHistoryWindow(parent, selected_date)
