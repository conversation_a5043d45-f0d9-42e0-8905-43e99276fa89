from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError
import logging
from tkinter import StringVar

# never change this source database content in POSTGRESQL
DB_CONFIGS = {
    "source": {
        "database": "i4_TokoLarisLAX",
        "host": "************",
        "user": "admin",
        "password": "2356988",
        "port": "5444"
    },
    "target": {
        "database": "i4_TokoLaris",
        "host": "************",
        "user": "admin",
        "password": "2356988",
        "port": "5444"
    }
}

# Global variable for status
status_var = None

def set_status_var(var):
    global status_var
    status_var = var

def create_engine_with_timeout(config, timeout=5):
    """Create engine with timeout settings"""
    return create_engine(
        f'postgresql://{config["user"]}:{config["password"]}'
        f'@{config["host"]}:{config["port"]}/{config["database"]}',
        pool_pre_ping=True,  # Enable connection health checks
        pool_recycle=3600,   # Recycle connections after 1 hour
        connect_args={'connect_timeout': timeout}
    )

# Create SQLAlchemy engines with timeout
try:
    if status_var:
        status_var.set("Connecting to source database...")
    engines = {
        "source": create_engine_with_timeout(DB_CONFIGS["source"]),
        "target": create_engine_with_timeout(DB_CONFIGS["target"])
    }

    # Create sessionmakers
    Sessions = {
        "source": sessionmaker(bind=engines["source"]),
        "target": sessionmaker(bind=engines["target"])
    }

    # For backward compatibility with existing code
    Session = Sessions["source"]

    if status_var:
        status_var.set("Connected to database")

except OperationalError as e:
    if status_var:
        status_var.set("Database connection failed")
    logging.error(f"Failed to connect to database: {str(e)}")
    raise 