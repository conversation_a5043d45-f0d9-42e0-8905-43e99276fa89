from sqlalchemy.orm import sessionmaker
import tkinter as tk
from tkinter import ttk
from tkcalendar import DateEntry
import random
from datetime import datetime, timedelta
import logging
from tkinter import messagebox
from decimal import Decimal
from sqlalchemy import and_
from sqlalchemy.exc import OperationalError, SQLAlchemyError
import threading
import locale
from babel.dates import format_date

from database.config import Session, Sessions, set_status_var
from database.models import SalesHeader, SalesDetail, ItemStock, ItemSatuanJml
from stock_adjustment import show_stock_adjustment
from sales_history import show_sales_history

# After imports, add logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class CustomDateEntry(DateEntry):
    def __init__(self, master=None, **kw):
        super().__init__(master, **kw)
        # Remove all custom formatting
        self.bind('<FocusOut>', self._update_display)
        self._update_display()
        
    def _update_display(self, event=None):
        date = self.get_date()
        formatted = date.strftime('%Y-%m-%d')
        
        # Update entry text directly
        self.delete(0, 'end')
        self.insert(0, formatted)

class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Sales Management")
        
        # Set default window size
        self.root.geometry("1200x800")
        
        # Add status bar for connection status
        self.status_var = tk.StringVar()
        self.status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN)
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.status_var.set("Connecting to database...")
        
        # Configure root grid to expand
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # Create frame
        frame = ttk.Frame(root, padding="10")
        frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure frame grid to expand
        frame.grid_rowconfigure(2, weight=1)  # Row with treeview should expand
        frame.grid_columnconfigure(0, weight=3)  # Main column gets more weight
        frame.grid_columnconfigure(1, weight=1)  # Column for total label
        
        # Left side container frame for date and buttons
        left_frame = ttk.Frame(frame)
        left_frame.grid(row=0, column=0, padx=5, pady=5, sticky='w')
        
        # Date selector label and navigation in first row
        ttk.Label(left_frame, text="Select Date:").grid(row=0, column=0, padx=5, pady=5)
        
        # Date navigation frame
        date_frame = ttk.Frame(left_frame)
        date_frame.grid(row=0, column=1, padx=5, pady=5)
        
        # Today button - now first
        ttk.Button(date_frame, text="Today", width=6,
                   command=self.go_to_today).grid(row=0, column=0, padx=2)
        
        # Left arrow button - moved to column 1
        ttk.Button(date_frame, text="←", width=3,
                   command=self.previous_date).grid(row=0, column=1, padx=2)
        
        # Date selector in the middle - moved to column 2
        self.date_selector = DateEntry(date_frame, width=15, background='darkblue',
                                     foreground='white', borderwidth=2)
        self.date_selector.grid(row=0, column=2, padx=2)
        
        # Right arrow button - moved to column 3
        ttk.Button(date_frame, text="→", width=3,
                   command=self.next_date).grid(row=0, column=3, padx=2)
        # Add range input field - already in correct position
        ttk.Label(date_frame, text="Max Total (Million):").grid(row=0, column=4, padx=5)
        
        # Create a frame for the range entry and buttons
        range_frame = ttk.Frame(date_frame)
        range_frame.grid(row=0, column=5, padx=2)
        
        # Decrease button
        ttk.Button(range_frame, text="-", width=2, 
                  command=lambda: self.adjust_range(-0.5)).grid(row=0, column=0)
        
        # Range entry
        self.range_entry = ttk.Entry(range_frame, width=5)
        self.range_entry.grid(row=0, column=1, padx=2)
        self.range_entry.insert(0, "6")  # Default value of 6 million
        
        # Increase button
        ttk.Button(range_frame, text="+", width=2,
                  command=lambda: self.adjust_range(0.5)).grid(row=0, column=2)

        
        # Process and Copy buttons in second row
        button_frame = ttk.Frame(left_frame)
        button_frame.grid(row=1, column=0, columnspan=2, padx=5, pady=5)
        
        # Process button
        ttk.Button(button_frame, text="Process Sales", 
                   command=self.process_sales).grid(row=0, column=0, padx=5)
        
        # Copy to Target DB button
        self.copy_button = ttk.Button(button_frame, text="Copy to Target DB", 
                                     command=self.handle_copy_to_target, state='disabled')
        self.copy_button.grid(row=0, column=1, padx=5)
        
        # Sync Items button
        ttk.Button(button_frame, text="Sync Items", 
                  command=self.handle_sync_items).grid(row=0, column=2, padx=5)
        
        # Add Compare DB button
        ttk.Button(button_frame, text="Compare DB", 
                  command=lambda: self.handle_compare_db()).grid(row=0, column=3, padx=5)
        
        # Add Stock Adjustment button
        ttk.Button(button_frame, text="Stock Adjustment", 
                  command=lambda: show_stock_adjustment(self.root)).grid(row=0, column=4, padx=5)
        
        # Add Sales History button
        ttk.Button(button_frame, text="Sales History", 
                  command=lambda: show_sales_history(self, self.date_selector.get_date())  # Changed from self.root to self
                  ).grid(row=0, column=5, padx=5)
        
        # Add Item button
        ttk.Button(button_frame, text="Add Item",
                  command=self.handle_add_item).grid(row=0, column=6, padx=5)
        
        # Create a frame for totals on the right side
        totals_frame = ttk.Frame(frame)
        totals_frame.grid(row=0, column=1, padx=5, pady=5, sticky='ne')
        
        # Total sales label
        self.total_label = ttk.Label(totals_frame, text="Total Sales: Rp 0")
        self.total_label.grid(row=0, column=0, padx=5, pady=2, sticky='e')
        
        # Total headers label
        self.headers_label = ttk.Label(totals_frame, text="Total Headers: 0")
        self.headers_label.grid(row=1, column=0, padx=5, pady=2, sticky='e')
        
        # Results table (spans full width)
        self.tree = ttk.Treeview(frame, columns=('Receipt', 'Date', 'Total'), show='headings')
        self.tree.heading('Receipt', text='Receipt No')
        self.tree.heading('Date', text='Date')
        self.tree.heading('Total', text='Total')
        self.tree.grid(row=2, column=0, columnspan=2, padx=5, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure column widths for the tree
        self.tree.column('Receipt', width=200, minwidth=150)
        self.tree.column('Date', width=200, minwidth=150)
        self.tree.column('Total', width=200, minwidth=150)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=self.tree.yview)
        scrollbar.grid(row=2, column=2, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Add instance variable to store filtered sales
        self.current_filtered_sales = None

    def process_sales(self):
        # Disable UI elements
        self.disable_controls()
        
        # Run database operation in separate thread
        thread = threading.Thread(target=self._process_sales_thread)
        thread.daemon = True
        thread.start()

    def _process_sales_thread(self):
        try:
            selected_date = self.date_selector.get_date()
            formatted_date = datetime.combine(selected_date, datetime.min.time())
            
            # Get the desired range from entry (in millions)
            try:
                max_total = float(self.range_entry.get()) * 1000000
            except ValueError:
                self.root.after(0, lambda: self._show_db_error("Invalid range value. Please enter a number."))
                return
                
            logging.info(f"Selected date: {formatted_date}, Max total: {max_total}")
            
            # Update status
            self.root.after(0, lambda: self.status_var.set("Querying database..."))
            
            session = Session()
            
            try:
                # Get all sales for the selected date
                next_day = formatted_date + timedelta(days=1)
                sales = session.query(SalesHeader).filter(
                    SalesHeader.tanggal >= formatted_date,
                    SalesHeader.tanggal < next_day,
                    SalesHeader.notransaksi.like('%/KSR/%')
                ).all()
                
                sales_count = len(sales)
                logging.info(f"Found {sales_count} KSR sales records for selected date")
                
                if not sales:
                    logging.warning("No KSR sales found for selected date")
                    # Clear the table and reset totals
                    self.root.after(0, lambda: self.update_display([]))
                    self.root.after(0, lambda: self._update_title(
                        f"No KSR sales found for date {formatted_date.strftime('%Y-%m-%d')}"
                    ))
                    # Update status when no sales found
                    self.root.after(0, lambda: self.status_var.set("Connected to database"))
                    return
                
                # Work with a copy of the data in memory
                sales_list = [(sale.notransaksi, sale.totalakhir, sale.tanggal) for sale in sales]
                total_sales = sum(sale.totalakhir for sale in sales)
                filtered_sales = sales_list.copy()
                
                # Update title with original count and total - will keep this throughout processing
                self.root.after(0, lambda: self._update_title(
                    f"Sales Management - {sales_count} KSR sales with total Rp {total_sales:,.2f}"
                ))
                
                logging.info(f"Initial total sales: {total_sales:,.2f}")
                current_total = total_sales
                
                # Remove sales from memory list only until we reach desired range
                while current_total > max_total and filtered_sales:
                    sale_to_remove = random.choice(filtered_sales)
                    filtered_sales.remove(sale_to_remove)
                    current_total -= sale_to_remove[1]
                    logging.info(f"Removing from display: {sale_to_remove[0]} with amount {sale_to_remove[1]:,.2f}")
                    logging.info(f"New display total: {current_total:,.2f}")
                
                # Update UI in main thread
                self.root.after(0, self._update_ui_after_processing, filtered_sales)
                
                # Update status when done
                self.root.after(0, lambda: self.status_var.set("Connected to database"))
                
            except OperationalError as e:
                self.root.after(0, lambda: self.status_var.set("Database connection failed"))
                self.root.after(0, self._show_db_error, "Database connection failed")
                logging.error(f"Database connection error: {str(e)}")
            except SQLAlchemyError as e:
                self.root.after(0, lambda: self.status_var.set("Database error occurred"))
                self.root.after(0, self._show_db_error, "Database error occurred")
                logging.error(f"Database error: {str(e)}")
            finally:
                session.close()
                
        except Exception as e:
            self.root.after(0, self._show_db_error, f"Error: {str(e)}")
            logging.error(f"Error in process_sales: {str(e)}", exc_info=True)
        finally:
            self.root.after(0, self.enable_controls)

    def _show_db_error(self, message):
        """Show database error message"""
        tk.messagebox.showerror("Database Error", message)

    def _update_ui_after_processing(self, filtered_sales):
        """Update UI with processed sales"""
        self.current_filtered_sales = filtered_sales
        self.update_display(filtered_sales)
        self.copy_button.config(state='normal')
        # Removed title update to keep showing original total

    def disable_controls(self):
        """Disable UI controls during processing"""
        self.date_selector.config(state='disabled')
        for child in self.root.winfo_children():
            if isinstance(child, ttk.Button):
                child.config(state='disabled')

    def enable_controls(self):
        """Enable UI controls after processing"""
        self.date_selector.config(state='normal')
        for child in self.root.winfo_children():
            if isinstance(child, ttk.Button):
                child.config(state='normal')
        # Only disable copy button if we don't have filtered sales
        if not self.current_filtered_sales:
            self.copy_button.config(state='disabled')

    def update_display(self, sales_list):
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # Add updated sales
        for sale in sales_list:
            self.tree.insert('', 'end', values=(
                sale[0],  # notransaksi
                sale[2].strftime('%Y-%m-%d %H:%M:%S.000'),  # actual datetime from DB
                f"{sale[1]:,.2f}"  # totalakhir
            ))
        
        # Update total labels
        total = sum(sale[1] for sale in sales_list)
        self.total_label.config(text=f"Total Sales: Rp {total:,.2f}")
        self.headers_label.config(text=f"Total Headers: {len(sales_list)}")

    def handle_copy_to_target(self):
        if not self.current_filtered_sales:
            logging.warning("No processed sales to copy")
            return
        
        self.disable_controls()
        
        thread = threading.Thread(target=self._copy_to_target_thread)
        thread.daemon = True
        thread.start()

    def _copy_to_target_thread(self):
        try:
            # Update status
            self.root.after(0, lambda: self.status_var.set("Copying to target database..."))
            
            self.copy_to_target_db(self.current_filtered_sales)
            self.root.after(0, self._show_success_message)
            # Update status when done
            self.root.after(0, lambda: self.status_var.set("Connected to database"))
        except ValueError as e:
            error_message = str(e)
            self.root.after(0, lambda: self.status_var.set("Copy operation failed"))
            self.root.after(0, lambda: self._show_copy_error("Cannot Copy", error_message))
        except Exception as e:
            error_message = str(e)
            self.root.after(0, lambda: self.status_var.set("Copy operation failed"))
            self.root.after(0, lambda: self._show_copy_error("Error", f"Failed to copy data: {error_message}"))
        finally:
            self.root.after(0, self.enable_controls)

    def _show_success_message(self):
        """Show success message for copy operation"""
        tk.messagebox.showinfo("Success", "Sales data copied to target database successfully!")

    def _show_copy_error(self, title, message):
        """Show error message for copy operation"""
        tk.messagebox.showerror(title, message)

    def copy_to_target_db(self, filtered_sales):
        try:
            source_session = Sessions["source"]()
            target_session = Sessions["target"]()
            
            # Get the date from the first sale (they're all from same date)
            sale_date = filtered_sales[0][2]  # Using the datetime from the tuple
            next_day = sale_date + timedelta(days=1)
            
            # Check if there are any sales for this date in target DB
            existing_sales = target_session.query(SalesHeader).filter(
                SalesHeader.tanggal >= sale_date,
                SalesHeader.tanggal < next_day
            ).first()
            
            if existing_sales:
                raise ValueError(f"Sales data already exists in target database for date {sale_date.strftime('%Y-%m-%d')}")
            
            # Get full sales data for the filtered receipts
            notransaksi_list = [sale[0] for sale in filtered_sales]
            
            # Get headers and details from source
            headers = source_session.query(SalesHeader).filter(
                SalesHeader.notransaksi.in_(notransaksi_list)
            ).all()
            
            details = source_session.query(SalesDetail).filter(
                SalesDetail.notransaksi.in_(notransaksi_list)
            ).all()
            
            logging.info(f"Copying {len(headers)} headers and {len(details)} details to target DB")
            
            # Process stock updates for target DB only
            stock_updates = {}  # {kodeitem: total_deduction}
            
            # Get all unique kodeitem from details
            kodeitem_list = list(set(detail.kodeitem for detail in details))
            
            # Get conversion rates for all items from source DB
            conversions = source_session.query(ItemSatuanJml).filter(
                ItemSatuanJml.kodeitem.in_(kodeitem_list)
            ).all()
            
            # Create conversion lookup dictionary
            conversion_dict = {}
            for conv in conversions:
                if conv.kodeitem not in conversion_dict:
                    conversion_dict[conv.kodeitem] = {}
                conversion_dict[conv.kodeitem][conv.satuan] = {
                    'jumlahkonv': float(conv.jumlahkonv) if conv.jumlahkonv else 1,
                    'tipe': conv.tipe
                }
            
            # Calculate stock deductions
            for detail in details:
                kodeitem = detail.kodeitem
                jumlah = float(detail.jumlah)
                satuan = detail.satuan
                
                # Get conversion rate
                if kodeitem in conversion_dict and satuan in conversion_dict[kodeitem]:
                    conv_info = conversion_dict[kodeitem][satuan]
                    if conv_info['tipe'] == 'K':  # If unit konversi, convert to unit dasar
                        jumlah = jumlah * conv_info['jumlahkonv']
                
                # Add to stock updates
                if kodeitem not in stock_updates:
                    stock_updates[kodeitem] = 0
                stock_updates[kodeitem] += jumlah
            
            # Update stocks in target database only
            for kodeitem, deduction in stock_updates.items():
                # Get current stock from source DB for reference
                source_stock = source_session.query(ItemStock).filter(
                    ItemStock.kodeitem == kodeitem
                ).first()
                
                if source_stock:
                    current_stock = float(source_stock.stok) if source_stock.stok else 0
                    new_stock = current_stock - deduction
                    
                    # Update target DB stock only
                    target_stock = target_session.query(ItemStock).filter(
                        ItemStock.kodeitem == kodeitem
                    ).first()
                    
                    if not target_stock:
                        target_stock = ItemStock(kodeitem=kodeitem)
                        target_session.add(target_stock)
                    
                    target_stock.stok = Decimal(str(new_stock))
            
            # Copy sales data to target
            for header in headers:
                new_header = SalesHeader(
                    **{c.key: getattr(header, c.key) 
                       for c in SalesHeader.__table__.columns}
                )
                target_session.merge(new_header)
            
            for detail in details:
                new_detail = SalesDetail(
                    **{c.key: getattr(detail, c.key) 
                       for c in SalesDetail.__table__.columns}
                )
                target_session.merge(new_detail)
            
            # Commit changes to target DB only
            target_session.commit()
            logging.info("Successfully copied data and updated stocks in target database")
            
        except Exception as e:
            logging.error(f"Error copying to target database: {str(e)}", exc_info=True)
            target_session.rollback()
            raise
        finally:
            source_session.close()
            target_session.close()

    def previous_date(self):
        current_date = self.date_selector.get_date()
        new_date = current_date - timedelta(days=1)
        self.date_selector.set_date(new_date)

    def next_date(self):
        current_date = self.date_selector.get_date()
        new_date = current_date + timedelta(days=1)
        self.date_selector.set_date(new_date)

    def go_to_today(self):
        """Set date selector to today's date"""
        today = datetime.now().date()
        self.date_selector.set_date(today)

    def _show_info_message(self, title, message):
        """Show information message"""
        tk.messagebox.showinfo(title, message)

    def _update_title(self, message):
        """Update window title"""
        self.root.title(message)

    def handle_sync_items(self):
        from sync_items import sync_items
        sync_items(self.root)

    def handle_compare_db(self):
        from compare_db import show_comparison_window
        show_comparison_window(self.root)

    def handle_add_item(self):
        from add_item import show_add_item_dialog
        show_add_item_dialog(self.root)
        
    def adjust_range(self, delta):
        """Adjust the range value by the given delta"""
        try:
            current_value = float(self.range_entry.get())
            new_value = max(0.5, current_value + delta)  # Ensure value doesn't go below 0.5
            self.range_entry.delete(0, tk.END)
            self.range_entry.insert(0, str(new_value))
        except ValueError:
            # If current value is not a valid number, reset to default
            self.range_entry.delete(0, tk.END)
            self.range_entry.insert(0, "6")


def main():
    root = tk.Tk()
    app = App(root)
    set_status_var(app.status_var)
    root.mainloop()

if __name__ == "__main__":
    main()
