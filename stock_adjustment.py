import tkinter as tk
from tkinter import ttk, messagebox
import logging
from decimal import Decimal
from database.config import Sessions
from database.models import Item, ItemStock, ItemSatuanJml, ItemOpname
from sqlalchemy import and_
from datetime import datetime

class StockAdjustmentDialog:
    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Stock Adjustment (Target DB)")
        self.dialog.geometry("1000x600")
        
        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Initialize current_edit
        self.current_edit = None
        
        # Create main frame
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Create filter frame
        filter_frame = ttk.LabelFrame(main_frame, text="Filter", padding="5")
        filter_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Stock threshold filter
        ttk.Label(filter_frame, text="Show items with stock ≤").grid(row=0, column=0, padx=5)
        self.threshold_var = tk.StringVar(value="0")
        self.threshold_entry = ttk.Entry(filter_frame, textvariable=self.threshold_var, width=10)
        self.threshold_entry.grid(row=0, column=1, padx=5)
        
        # Apply filter button
        ttk.Button(filter_frame, text="Apply Filter", command=self.apply_filter).grid(
            row=0, column=2, padx=5)
        
        # Bulk update frame
        bulk_frame = ttk.Frame(filter_frame)
        bulk_frame.grid(row=0, column=3, padx=20)
        
        ttk.Label(bulk_frame, text="Set all filtered stocks to:").grid(row=0, column=0, padx=5)
        self.bulk_value_var = tk.StringVar()
        self.bulk_value_entry = ttk.Entry(bulk_frame, textvariable=self.bulk_value_var, width=10)
        self.bulk_value_entry.grid(row=0, column=1, padx=5)
        
        ttk.Button(bulk_frame, text="Apply to All", command=self.apply_bulk_update).grid(
            row=0, column=2, padx=5)
        
        # Create treeview
        self.tree = ttk.Treeview(main_frame, 
                                columns=('kodeitem', 'namaitem', 'stok', 'satuan'),
                                show='headings')
        
        self.tree.heading('kodeitem', text='Kode Item')
        self.tree.heading('namaitem', text='Nama Item')
        self.tree.heading('stok', text='Stok')
        self.tree.heading('satuan', text='Satuan')
        
        # Configure column widths
        self.tree.column('kodeitem', width=150)
        self.tree.column('namaitem', width=300)
        self.tree.column('stok', width=100)
        self.tree.column('satuan', width=100)
        
        # Add scrollbars
        vsb = ttk.Scrollbar(main_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(main_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # Grid layout for treeview and scrollbars
        self.tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        vsb.grid(row=1, column=1, sticky=(tk.N, tk.S))
        hsb.grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, pady=10)
        
        # Save and Close buttons
        ttk.Button(button_frame, text="Save Changes", command=self.save_changes).grid(
            row=0, column=0, padx=5)
        ttk.Button(button_frame, text="Close", command=self.dialog.destroy).grid(
            row=0, column=1, padx=5)
        
        # Bind double-click event
        self.tree.bind('<ButtonRelease-1>', self.handle_click)
        
        # Add instruction label
        ttk.Label(main_frame, 
                 text="Click stock value to edit. Press Enter to save and move to next item.", 
                 foreground='gray').grid(row=4, column=0, pady=(5,0))
        
        # Store original values and changes
        self.original_values = {}
        self.changes = {}
        
        # Initial load
        self.apply_filter()
    
    def apply_filter(self):
        try:
            threshold = float(self.threshold_var.get())
            
            # Clear existing items
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Use target session instead of source
            session = Sessions["target"]()
            
            # Query items with stock <= threshold
            items = session.query(Item, ItemStock).join(
                ItemStock, Item.kodeitem == ItemStock.kodeitem
            ).filter(
                ItemStock.stok <= threshold
            ).all()
            
            # Populate treeview and store original values
            self.original_values.clear()
            for item, stock in items:
                stok_value = float(stock.stok) if stock.stok else 0
                # Ensure kodeitem is stored as string
                self.original_values[str(item.kodeitem)] = stok_value

                self.tree.insert('', 'end', values=(
                    str(item.kodeitem),  # Ensure kodeitem is string in treeview
                    item.namaitem,
                    f"{stok_value:,.2f}",
                    item.satuan
                ))
            
            session.close()
            
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number for threshold")
        except Exception as e:
            logging.error(f"Error applying filter: {str(e)}", exc_info=True)
            messagebox.showerror("Error", f"Failed to apply filter: {str(e)}")
    
    def handle_click(self, event):
        # Get the item and column that was clicked
        item = self.tree.identify_row(event.y)
        column = self.tree.identify_column(event.x)
        
        # Only proceed if we clicked an item in the stock column (#3)
        if item and column == '#3':
            self.start_edit(item)
    
    def start_edit(self, item):
        # If there's already an edit in progress, save it first
        if self.current_edit:
            self.current_edit.destroy()
            self.current_edit = None
        
        # Select the item
        self.tree.selection_set(item)
        self.tree.focus(item)
        
        # Get the bounding box of the stock cell
        column = '#3'  # Stock column
        x, y, w, h = self.tree.bbox(item, column)
        
        # Create entry widget for editing
        value = self.tree.item(item)['values'][2]
        entry = ttk.Entry(self.tree, width=15)
        entry.place(x=x, y=y, width=w, height=h)
        entry.insert(0, value)
        entry.select_range(0, tk.END)
        
        def move_to_next_item():
            items = self.tree.get_children()
            current_index = items.index(item)
            if current_index < len(items) - 1:
                next_item = items[current_index + 1]
                self.tree.selection_set(next_item)
                self.tree.focus(next_item)
                self.tree.see(next_item)  # Ensure next item is visible
                self.start_edit(next_item)
        
        def save_edit(event=None, move_next=False):
            try:
                new_value = float(entry.get().replace(',', ''))  # Remove commas before converting
                kodeitem = str(self.tree.item(item)['values'][0])  # Ensure kodeitem is string
                self.changes[kodeitem] = new_value
                self.tree.set(item, column='stok', value=f"{new_value:,.2f}")

                if move_next:
                    move_to_next_item()

            except ValueError:
                messagebox.showerror("Error", "Please enter a valid number")
            finally:
                if not move_next:
                    entry.destroy()
                    self.current_edit = None
        
        def handle_enter(event):
            save_edit(event, move_next=True)
            return 'break'  # Prevent default Enter behavior
        
        def handle_escape(event):
            entry.destroy()
            self.current_edit = None
            return 'break'
        
        def handle_tab(event):
            # Let tab work normally for focusing other widgets
            save_edit(event, move_next=False)
        
        entry.focus()
        entry.bind('<Return>', handle_enter)  # Enter saves and moves to next
        entry.bind('<Escape>', handle_escape)  # Escape cancels
        entry.bind('<Tab>', handle_tab)  # Tab saves and lets focus move normally
        entry.bind('<FocusOut>', lambda e: save_edit(e, move_next=False))
        
        # Store reference to current edit widget
        self.current_edit = entry
    
    def apply_bulk_update(self):
        try:
            new_value = float(self.bulk_value_var.get())

            # Update all visible items
            for item in self.tree.get_children():
                kodeitem = str(self.tree.item(item)['values'][0])  # Ensure kodeitem is string
                self.changes[kodeitem] = new_value
                self.tree.set(item, column='stok', value=f"{new_value:,.2f}")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number for bulk update")
    
    def save_changes(self):
        if not self.changes:
            messagebox.showinfo("Info", "No changes to save")
            return
        
        try:
            session = Sessions["target"]()
            
            # Get current date and zero out the time
            now = datetime.now()
            opname_date = datetime(now.year, now.month, now.day, 0, 0, 0)
            
            # Use current datetime for dateupd
            periode = f"OPNA1E-{opname_date.strftime('%Y%m%d')}"
            
            # Process only items where new value is different from original
            items_to_process = {}
            for kodeitem, new_stock in self.changes.items():
                original_stock = self.original_values.get(kodeitem)
                # If original_stock is None, it means the item was not in the initial filter,
                # so any change is considered significant.
                if original_stock is None or abs(new_stock - original_stock) > 0.001:
                    items_to_process[kodeitem] = new_stock
            
            if not items_to_process:
                messagebox.showinfo("Info", "No stock changes to save")
                return
            
            for kodeitem, jmlfisik in items_to_process.items():
                # Get current stock
                stock = session.query(ItemStock).filter(
                    ItemStock.kodeitem == str(kodeitem) # Ensure kodeitem is treated as string
                ).first()
                
                if stock:
                    jmlsebelum = float(stock.stok) if stock.stok else 0
                    jmlselisih = jmlfisik - jmlsebelum
                    
                    # Get item's base unit price
                    item_satuan = session.query(ItemSatuanJml).filter(
                        and_(
                            ItemSatuanJml.kodeitem == str(kodeitem), # Ensure kodeitem is treated as string
                            ItemSatuanJml.tipe == 'D'
                        )
                    ).first()
                    
                    harga = float(item_satuan.hargapokok) if item_satuan and item_satuan.hargapokok else 0
                    
                    # Create opname record with fixed date
                    opname = ItemOpname(
                        iddetail=f"{opname_date.strftime('%Y%m%d')}-{kodeitem}-UTM",
                        periode=periode,
                        tanggal=opname_date,  # Use fixed date here
                        kodeitem=str(kodeitem), # Ensure kodeitem is treated as string
                        kodekantor="UTM",
                        satuan=item_satuan.satuan if item_satuan else None,
                        jmlsebelum=Decimal(str(jmlsebelum)),
                        jmlfisik=Decimal(str(jmlfisik)),
                        jmlselisih=Decimal(str(jmlselisih)),
                        kodeacc="5-2200",
                        user1="J",
                        user2=None,
                        dateupd=now,  # Use current time for update timestamp
                        harga=Decimal(str(harga)),
                        total=Decimal(str(jmlselisih * harga)),
                        compname="RYZEN5600G"
                    )
                    session.add(opname)
                    
                    # Update stock
                    stock.stok = Decimal(str(jmlfisik))
            
            session.commit()
            messagebox.showinfo("Success", f"Successfully updated {len(items_to_process)} items")
            
            # Clear changes and refresh display
            self.changes.clear()
            self.apply_filter()
            
        except Exception as e:
            logging.error(f"Error saving changes: {str(e)}", exc_info=True)
            messagebox.showerror("Error", f"Failed to save changes: {str(e)}")
            session.rollback()
        finally:
            session.close()

def show_stock_adjustment(parent_window):
    StockAdjustmentDialog(parent_window)
