import tkinter as tk
from tkinter import ttk, messagebox
import logging
from database.config import Sessions
from database.models import ItemSatuanJml, ItemHargaJual, Item
from sqlalchemy import and_

#this function check the difference in price, item missing, unit missing its more complete than sync items

class ComparisonWindow:
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("Database Comparison")
        self.window.geometry("1000x600")
        
        # Create main frame
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.window.grid_rowconfigure(0, weight=1)
        self.window.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Add buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=0, column=0, pady=(0, 10), sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="Compare Databases", 
                  command=self.compare_databases).pack(side=tk.LEFT, padx=5)
        
        self.sync_button = ttk.Button(button_frame, text="Sync Selected", 
                                    command=self.sync_selected, state='disabled')
        self.sync_button.pack(side=tk.LEFT, padx=5)
        
        # Create notebook for different tables
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create frames for each table
        self.create_table_frame("ItemSatuanJml", ["kodeitem", "satuan", "hargapokok", "jumlahkonv"])
        self.create_table_frame("ItemHargaJual", ["kodeitem", "satuan", "hargajual", "tipehj"])
        self.create_table_frame("Item", ["kodeitem", "namaitem"])
        
        self.differences = {}

    def create_table_frame(self, table_name, columns):
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=table_name)
        
        # Create treeview with columns for both source and target values
        display_columns = ["source", "target", "kodeitem"]
        for col in columns[1:]:  # Skip kodeitem as it's already added
            display_columns.extend([f"source_{col}", f"target_{col}"])
        
        tree = ttk.Treeview(frame, columns=display_columns, show="headings", selectmode="extended")
        
        # Configure columns
        tree.heading("source", text="Source")
        tree.heading("target", text="Target")
        tree.heading("kodeitem", text="Kode Item")
        tree.column("source", width=50)
        tree.column("target", width=50)
        tree.column("kodeitem", width=100)
        
        # Configure columns for source and target values
        for col in columns[1:]:  # Skip kodeitem
            tree.heading(f"source_{col}", text=f"Source {col}")
            tree.heading(f"target_{col}", text=f"Target {col}")
            tree.column(f"source_{col}", width=120)
            tree.column(f"target_{col}", width=120)
        
        if table_name == "ItemHargaJual":
            # Add tipehj to the displayed columns
            display_columns.extend(["source_tipehj", "target_tipehj"])
            # Configure tipehj columns
            tree.heading("source_tipehj", text="Source TipeHJ")
            tree.heading("target_tipehj", text="Target TipeHJ")
            tree.column("source_tipehj", width=80)
            tree.column("target_tipehj", width=80)
        
        # Add scrollbars
        vsb = ttk.Scrollbar(frame, orient="vertical", command=tree.yview)
        hsb = ttk.Scrollbar(frame, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # Grid layout
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        vsb.grid(row=0, column=1, sticky=(tk.N, tk.S))
        hsb.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        frame.grid_rowconfigure(0, weight=1)
        frame.grid_columnconfigure(0, weight=1)
        
        setattr(self, f"tree_{table_name}", tree)

    def compare_databases(self):
        try:
            source_session = Sessions["source"]()
            target_session = Sessions["target"]()
            
            self.differences = {
                'ItemSatuanJml': [],
                'ItemHargaJual': [],
                'Item': []
            }
            
            try:
                # Compare ItemSatuanJml
                source_items = source_session.query(ItemSatuanJml).all()
                for source_item in source_items:
                    target_item = target_session.query(ItemSatuanJml).filter(
                        and_(
                            ItemSatuanJml.kodeitem == source_item.kodeitem,
                            ItemSatuanJml.satuan == source_item.satuan
                        )
                    ).first()
                    
                    if not target_item or \
                       source_item.hargapokok != target_item.hargapokok or \
                       source_item.jumlahkonv != target_item.jumlahkonv:
                        self.differences['ItemSatuanJml'].append({
                            'source': source_item,
                            'target': target_item
                        })
                
                # Compare ItemHargaJual
                source_items = source_session.query(ItemHargaJual).all()
                for source_item in source_items:
                    target_item = target_session.query(ItemHargaJual).filter(
                        and_(
                            ItemHargaJual.kodeitem == source_item.kodeitem,
                            ItemHargaJual.satuan == source_item.satuan,
                            ItemHargaJual.tipehj == source_item.tipehj
                        )
                    ).first()
                    
                    if not target_item or source_item.hargajual != target_item.hargajual:
                        self.differences['ItemHargaJual'].append({
                            'source': source_item,
                            'target': target_item
                        })
                
                # Compare Item
                source_items = source_session.query(Item).all()
                for source_item in source_items:
                    target_item = target_session.query(Item).filter(
                        Item.kodeitem == source_item.kodeitem
                    ).first()
                    
                    if not target_item or source_item.namaitem != target_item.namaitem:
                        self.differences['Item'].append({
                            'source': source_item,
                            'target': target_item
                        })
                
                self.update_trees()
                self.sync_button.config(state='normal' if any(self.differences.values()) else 'disabled')
                
                # Show success message
                messagebox.showinfo("Success", "Database comparison completed successfully")
                
            except Exception as e:
                error_msg = f"Error during database comparison: {str(e)}"
                logging.error(error_msg, exc_info=True)
                messagebox.showerror("Error", error_msg)
                
        except Exception as e:
            error_msg = f"Error connecting to databases: {str(e)}"
            logging.error(error_msg, exc_info=True)
            messagebox.showerror("Error", error_msg)
            
        finally:
            source_session.close()
            target_session.close()

    def update_trees(self):
        # Clear all trees
        for table in ['ItemSatuanJml', 'ItemHargaJual', 'Item']:
            tree = getattr(self, f"tree_{table}")
            for item in tree.get_children():
                tree.delete(item)
        
        # Update ItemSatuanJml tree
        for diff in self.differences['ItemSatuanJml']:
            source = diff['source']
            target = diff['target']
            values = ['✓', '✗' if not target else '≠', source.kodeitem]
            
            # Add source and target values for comparison
            values.extend([
                source.satuan,
                target.satuan if target else '',
                source.hargapokok,
                target.hargapokok if target else '',
                source.jumlahkonv,
                target.jumlahkonv if target else ''
            ])
            
            self.tree_ItemSatuanJml.insert('', 'end', values=values)
        
        # Update ItemHargaJual tree
        for diff in self.differences['ItemHargaJual']:
            source = diff['source']
            target = diff['target']
            values = ['✓', '✗' if not target else '≠', source.kodeitem]
            
            # Add source and target values for comparison including tipehj
            values.extend([
                source.satuan,
                target.satuan if target else '',
                source.tipehj,
                target.tipehj if target else '',
                source.hargajual,
                target.hargajual if target else ''
            ])
            
            self.tree_ItemHargaJual.insert('', 'end', values=values)
        
        # Update Item tree
        for diff in self.differences['Item']:
            source = diff['source']
            target = diff['target']
            values = ['✓', '✗' if not target else '≠', source.kodeitem]
            
            # Add source and target values for comparison
            values.extend([
                source.namaitem,
                target.namaitem if target else ''
            ])
            
            self.tree_Item.insert('', 'end', values=values)

    def sync_selected(self):
        if not any(self.differences.values()):
            messagebox.showinfo("Info", "No differences to sync")
            return
            
        if messagebox.askyesno("Confirm Sync", 
                              "Are you sure you want to sync the selected items to the target database?"):
            try:
                source_session = Sessions["source"]()
                target_session = Sessions["target"]()
                
                # Sync all differences
                for table, diffs in self.differences.items():
                    for diff in diffs:
                        source_item = diff['source']
                        # Copy all columns from source item
                        if table == 'ItemSatuanJml':
                            target_item = ItemSatuanJml()
                        elif table == 'ItemHargaJual':
                            target_item = ItemHargaJual()
                        else:  # Item
                            target_item = Item()
                            
                        # Copy all attributes from source to target
                        for column in source_item.__table__.columns:
                            setattr(target_item, column.key, getattr(source_item, column.key))
                            
                        target_session.merge(target_item)
                
                target_session.commit()
                messagebox.showinfo("Success", "Database sync completed successfully")
                
                # Refresh comparison
                self.compare_databases()
                
            except Exception as e:
                target_session.rollback()
                messagebox.showerror("Error", f"Error syncing databases: {str(e)}")
                logging.error(f"Error syncing databases: {str(e)}", exc_info=True)
            finally:
                source_session.close()
                target_session.close()

def show_comparison_window(parent):
    ComparisonWindow(parent)
