from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, String, Integer, Float, Date, DateTime, Text, Numeric, Boolean, LargeBinary
from sqlalchemy.sql import func

Base = declarative_base()

class SalesHeader(Base):
    __tablename__ = 'tbl_ikhd'
    
    notransaksi = Column(String, primary_key=True)
    kodekantor = Column(String)
    kantordari = Column(String)
    tanggal = Column(DateTime)
    tipe = Column(String)
    notrsorder = Column(String)
    kodesupel = Column(String)
    kodesales = Column(String)
    kodesales2 = Column(String)
    kodesales3 = Column(String)
    kodesales4 = Column(String)
    matauang = Column(String)
    rate = Column(Numeric)
    keterangan = Column(Text)
    totalitem = Column(Numeric)
    totalitempesan = Column(Numeric)
    subtotal = Column(Numeric)
    potfaktur = Column(Numeric)
    pajak = Column(Numeric)
    biayalain = Column(Numeric)
    totalakhir = Column(Numeric)
    carabayar = Column(String)
    jmltunai = Column(Numeric)
    jmlkredit = Column(Numeric)
    jmldebit = Column(Numeric)
    jmlkk = Column(Numeric)
    komisi1 = Column(Numeric)
    komisi2 = Column(Numeric)
    komisi3 = Column(Numeric)
    komisi4 = Column(Numeric)
    acc_potongan = Column(String)
    acc_pajak = Column(String)
    acc_biayalain = Column(String)
    acc_tunai = Column(String)
    acc_kredit = Column(String)
    acc_sales = Column(String)
    acc_hpp = Column(String)
    acc_debit = Column(String)
    acc_kk = Column(String)
    byr_krd_jt = Column(DateTime)
    byr_krd_no = Column(String)
    byr_debit_bank = Column(String)
    byr_kk_bank = Column(String)
    byr_debit_no = Column(String)
    byr_kk_no = Column(String)
    krd_jml_pot = Column(Numeric)
    krd_jml_byr = Column(Numeric)
    user1 = Column(String)
    user2 = Column(String)
    dateupd = Column(DateTime)
    tanggal_sa = Column(Date)
    biaya_msk_total = Column(Boolean)
    potnomfaktur = Column(Numeric)
    compname = Column(String)
    shiftkerja = Column(String)
    point_ik = Column(Numeric)
    point_sts = Column(Integer)
    acc_biaya_pot = Column(String)
    prpajak = Column(Numeric)
    nofp = Column(String)
    dppesanan = Column(Numeric)
    acc_dppesanan = Column(String)
    notrsretur = Column(String)

class SalesDetail(Base):
    __tablename__ = 'tbl_ikdt'
    
    iddetail = Column(String)
    nobaris = Column(Integer)
    notransaksi = Column(String, primary_key=True)
    kodeitem = Column(String, primary_key=True)
    jumlah = Column(Numeric)
    jmlpesan = Column(Numeric)
    satuan = Column(String)
    harga = Column(Numeric)
    potongan = Column(Numeric)
    potongan2 = Column(Numeric)
    potongan3 = Column(Numeric)
    potongan4 = Column(Numeric)
    total = Column(Numeric)
    pajak = Column(Numeric)
    jmlrmasuk = Column(Numeric)
    jmlkeluar = Column(Numeric)
    jmlrkeluar = Column(Numeric)
    jmlsisa = Column(Numeric)
    jmlkonsibayar = Column(Numeric)
    idorder = Column(String)
    dateupd = Column(DateTime)
    idtrsretur = Column(String)
    jmlretur = Column(Numeric)
    detinfo = Column(Text)

class ItemStock(Base):
    __tablename__ = 'tbl_itemstok'
    
    kodeitem = Column(String, primary_key=True)
    kantor = Column(String)
    stok = Column(Numeric) 

class ItemSatuanJml(Base):
    __tablename__ = 'tbl_itemsatuanjml'
    
    iddetail = Column(String)
    kodeitem = Column(String, primary_key=True)
    satuan = Column(String, primary_key=True)
    jumlahkonv = Column(Numeric)
    kodebarcode = Column(String)
    hargapokok = Column(Numeric)
    tipe = Column(String)
    dateupd = Column(DateTime) 

class ItemHargaJual(Base):
    __tablename__ = 'tbl_itemhj'
    
    iddetail = Column(String)
    kodeitem = Column(String, primary_key=True)
    tipehj = Column(String, primary_key=True)
    jmlsampai = Column(Numeric)
    level = Column(Integer)
    prosentase = Column(Numeric)
    satuan = Column(String, primary_key=True)
    hargajual = Column(Numeric)
    dateupd = Column(DateTime) 

class Item(Base):
    __tablename__ = 'tbl_item'
    
    kodeitem = Column(String, primary_key=True)
    namaitem = Column(Text)
    jenis = Column(String)
    tipe = Column(String)
    matauang = Column(String)
    serial = Column(String)
    konsinyasi = Column(String)
    stokmin = Column(Numeric)
    sistemhargajual = Column(String)
    opsihargajual = Column(Boolean)
    rak = Column(String)
    satuan = Column(String)
    hargapokok = Column(Numeric)
    prhargajual1 = Column(Numeric)
    hargajual1 = Column(Numeric)
    keterangan = Column(Text)
    supplier1 = Column(String)
    gambar = Column(LargeBinary)  # for bytea type
    statusjual = Column(String)
    acc_hpp = Column(String)
    acc_pendapatan = Column(String)
    acc_persediaan = Column(String)
    acc_jasa = Column(String)
    acc_noninventory = Column(String)
    statushapus = Column(String)
    tmphp = Column(Numeric)
    tmpjml = Column(Numeric)
    tmpnilai = Column(Numeric)
    dateupd = Column(DateTime)
    dept = Column(String)
    stok = Column(Numeric)
    merek = Column(String) 

class ItemOpname(Base):
    __tablename__ = 'tbl_itemopname'
    
    iddetail = Column(String, primary_key=True)  # Format: YYYYMMDD-kodeitem-UTM
    periode = Column(String)  # Format: OPNA1E-YYYYMMDD
    tanggal = Column(DateTime) # Format: current date with time set to 00:00:00.000
    kodeitem = Column(String)
    kodekantor = Column(String) # UTM
    satuan = Column(String)  # References tbl_itemsatuanjml.satuan where tipe='D'
    jmlsebelum = Column(Numeric)  # From tbl_itemstok.stok
    jmlfisik = Column(Numeric)  # User input
    jmlselisih = Column(Numeric)  # jmlfisik - jmlsebelum
    kodeacc = Column(String) # 5-2200
    user1 = Column(String) # J
    user2 = Column(String) # NULL
    dateupd = Column(DateTime) # Format: 2025-01-03 10:13:13.707
    harga = Column(Numeric)  # From tbl_itemsatuanjml.hargapokok where tipe='D'
    total = Column(Numeric)  # jmlselisih * harga
    compname = Column(String) # RYZEN5600G
