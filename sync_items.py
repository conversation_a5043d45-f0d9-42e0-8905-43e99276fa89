from sqlalchemy import select, func
from tkinter import messagebox
import tkinter as tk
from tkinter import ttk
import logging
from database.config import Sessions
from database.models import Item, ItemSatuanJml, ItemHargaJual, ItemStock
from stock_adjustment import show_stock_adjustment

#this function check for mission items and satuan in target database

class ItemSyncDialog:
    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Item Sync Preview")
        self.dialog.geometry("800x600")
        
        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Create main frame
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Add label
        ttk.Label(main_frame, text="Items to be copied to target database:").grid(
            row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        # Create notebook for different tables
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create frames for each table
        self.items_frame = ttk.Frame(self.notebook)
        self.satuan_frame = ttk.Frame(self.notebook)
        self.hj_frame = ttk.Frame(self.notebook)
        self.stock_frame = ttk.Frame(self.notebook)
        
        self.notebook.add(self.items_frame, text='Items')
        self.notebook.add(self.satuan_frame, text='Satuan')
        self.notebook.add(self.hj_frame, text='Harga Jual')
        self.notebook.add(self.stock_frame, text='Stock')
        
        # Create treeviews
        self.items_tree = self.create_items_treeview(self.items_frame)
        self.satuan_tree = self.create_satuan_treeview(self.satuan_frame)
        self.hj_tree = self.create_hj_treeview(self.hj_frame)
        self.stock_tree = self.create_stock_treeview(self.stock_frame)
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, pady=10)
        
        # Add buttons
        ttk.Button(button_frame, text="Process Copy", command=self.confirm).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self.cancel).grid(row=0, column=1, padx=5)
        
        self.result = False
        
    def create_items_treeview(self, parent):
        tree = ttk.Treeview(parent, columns=('kodeitem', 'namaitem', 'satuan'), show='headings')
        tree.heading('kodeitem', text='Kode Item')
        tree.heading('namaitem', text='Nama Item')
        tree.heading('satuan', text='Satuan')
        
        # Add scrollbars
        vsb = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        hsb = ttk.Scrollbar(parent, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # Grid layout
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        vsb.grid(row=0, column=1, sticky=(tk.N, tk.S))
        hsb.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        
        return tree
        
    def create_satuan_treeview(self, parent):
        tree = ttk.Treeview(parent, columns=('kodeitem', 'satuan', 'jumlahkonv', 'tipe'), show='headings')
        tree.heading('kodeitem', text='Kode Item')
        tree.heading('satuan', text='Satuan')
        tree.heading('jumlahkonv', text='Jumlah Konversi')
        tree.heading('tipe', text='Tipe')
        
        # Add scrollbars
        vsb = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        hsb = ttk.Scrollbar(parent, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # Grid layout
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        vsb.grid(row=0, column=1, sticky=(tk.N, tk.S))
        hsb.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        
        return tree
        
    def create_hj_treeview(self, parent):
        tree = ttk.Treeview(parent, columns=('kodeitem', 'satuan', 'hargajual'), show='headings')
        tree.heading('kodeitem', text='Kode Item')
        tree.heading('satuan', text='Satuan')
        tree.heading('hargajual', text='Harga Jual')
        
        # Add scrollbars
        vsb = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        hsb = ttk.Scrollbar(parent, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # Grid layout
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        vsb.grid(row=0, column=1, sticky=(tk.N, tk.S))
        hsb.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        
        return tree

    def create_stock_treeview(self, parent):
        tree = ttk.Treeview(parent, columns=('kodeitem', 'kantor', 'stok'), show='headings')
        tree.heading('kodeitem', text='Kode Item')
        tree.heading('kantor', text='Kantor')
        tree.heading('stok', text='Stok')
        
        # Add scrollbars
        vsb = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        hsb = ttk.Scrollbar(parent, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # Grid layout
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        vsb.grid(row=0, column=1, sticky=(tk.N, tk.S))
        hsb.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        
        return tree

    def populate_data(self, items_data, satuan_data, hj_data, stock_data):
        # Clear existing items
        for tree in [self.items_tree, self.satuan_tree, self.hj_tree, self.stock_tree]:
            for item in tree.get_children():
                tree.delete(item)
        
        # Populate items
        for item in items_data:
            self.items_tree.insert('', 'end', values=(
                item.kodeitem,
                item.namaitem,
                item.satuan
            ))
        
        # Populate satuan
        for satuan in satuan_data:
            self.satuan_tree.insert('', 'end', values=(
                satuan.kodeitem,
                satuan.satuan,
                satuan.jumlahkonv,
                satuan.tipe
            ))
        
        # Populate harga jual
        for hj in hj_data:
            self.hj_tree.insert('', 'end', values=(
                hj.kodeitem,
                hj.satuan,
                f"{float(hj.hargajual):,.2f}" if hj.hargajual else "0.00"
            ))
        
        # Add stock population
        for stock in stock_data:
            self.stock_tree.insert('', 'end', values=(
                stock.kodeitem,
                stock.kantor,
                f"{float(stock.stok):,.2f}" if stock.stok else "0.00"
            ))

    def confirm(self):
        self.result = True
        self.dialog.destroy()
        
    def cancel(self):
        self.result = False
        self.dialog.destroy()

def sync_items(parent_window):
    try:
        source_session = Sessions["source"]()
        target_session = Sessions["target"]()
        
        # Get all kodeitem-satuan combinations from both databases
        source_items = source_session.query(
            ItemSatuanJml.kodeitem, 
            ItemSatuanJml.satuan
        ).all()
        
        target_items = target_session.query(
            ItemSatuanJml.kodeitem, 
            ItemSatuanJml.satuan
        ).all()
        
        # Create sets of (kodeitem, satuan) tuples for comparison
        source_combinations = {(item.kodeitem, item.satuan) for item in source_items}
        target_combinations = {(item.kodeitem, item.satuan) for item in target_items}
        
        # Find missing combinations
        missing_combinations = source_combinations - target_combinations
        
        if not missing_combinations:
            messagebox.showinfo("Sync Items", "No missing items or satuan combinations found in target database.")
            return
        
        # Get kodeitem list from missing combinations
        missing_kodeitem = {combo[0] for combo in missing_combinations}
        
        # Get full data for missing items
        missing_items = source_session.query(Item).filter(
            Item.kodeitem.in_(missing_kodeitem)
        ).all()
        
        # Get all satuan data for these items
        missing_satuan = source_session.query(ItemSatuanJml).filter(
            ItemSatuanJml.kodeitem.in_(missing_kodeitem)
        ).all()
        
        missing_hj = source_session.query(ItemHargaJual).filter(
            ItemHargaJual.kodeitem.in_(missing_kodeitem)
        ).all()
        
        missing_stock = source_session.query(ItemStock).filter(
            ItemStock.kodeitem.in_(missing_kodeitem)
        ).all()
        
        # Show preview dialog
        dialog = ItemSyncDialog(parent_window)
        dialog.populate_data(missing_items, missing_satuan, missing_hj, missing_stock)
        dialog.dialog.wait_window()
        
        if dialog.result:
            # For each kodeitem, check if it exists in target
            for kodeitem in missing_kodeitem:
                # Check if item exists in target
                existing_item = target_session.query(Item).filter(
                    Item.kodeitem == kodeitem
                ).first()
                
                if not existing_item:
                    # If item doesn't exist, add it
                    item = next(item for item in missing_items if item.kodeitem == kodeitem)
                    new_item = Item(
                        **{c.key: getattr(item, c.key) 
                           for c in Item.__table__.columns}
                    )
                    target_session.add(new_item)
            
            # Copy missing satuan combinations
            for satuan in missing_satuan:
                if (satuan.kodeitem, satuan.satuan) in missing_combinations:
                    new_satuan = ItemSatuanJml(
                        **{c.key: getattr(satuan, c.key) 
                           for c in ItemSatuanJml.__table__.columns}
                    )
                    target_session.add(new_satuan)
            
            # Copy harga jual for missing combinations
            for hj in missing_hj:
                if (hj.kodeitem, hj.satuan) in missing_combinations:
                    new_hj = ItemHargaJual(
                        **{c.key: getattr(hj, c.key) 
                           for c in ItemHargaJual.__table__.columns}
                    )
                    target_session.add(new_hj)
            
            # Copy or update stock
            for stock in missing_stock:
                existing_stock = target_session.query(ItemStock).filter(
                    ItemStock.kodeitem == stock.kodeitem
                ).first()
                
                if not existing_stock:
                    new_stock = ItemStock(
                        **{c.key: getattr(stock, c.key) 
                           for c in ItemStock.__table__.columns}
                    )
                    target_session.add(new_stock)
            
            target_session.commit()
            messagebox.showinfo("Success", 
                f"Successfully synced {len(missing_combinations)} item-satuan combinations to target database")
            logging.info(f"Synced {len(missing_combinations)} item-satuan combinations to target database")
        
    except Exception as e:
        logging.error(f"Error in sync_items: {str(e)}", exc_info=True)
        messagebox.showerror("Error", f"Failed to sync items: {str(e)}")
        target_session.rollback()
    finally:
        source_session.close()
        target_session.close() 