import tkinter as tk
from tkinter import ttk, messagebox
import logging
from database.config import Sessions
from database.models import Item, ItemSatuanJml, ItemHargaJual

# Search an item kodeitem or barcode and if found copy to target database

class AddItemDialog:
    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add Item")
        self.dialog.geometry("800x600")
        
        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Create main frame
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights for dialog and main_frame
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)  # Make results_frame expandable
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Search frame
        search_frame = ttk.Frame(main_frame)
        search_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(search_frame, text="Kode Item:").grid(row=0, column=0, padx=5)
        self.kodeitem_var = tk.StringVar()
        self.kodeitem_entry = ttk.Entry(search_frame, textvariable=self.kodeitem_var)
        self.kodeitem_entry.grid(row=0, column=1, padx=5)
        
        # Bind Enter key to search function
        self.kodeitem_entry.bind('<Return>', lambda e: self.search_item())
        
        ttk.Button(search_frame, text="Search", 
                  command=self.search_item).grid(row=0, column=2, padx=5)
        
        # Set focus to entry
        self.kodeitem_entry.focus()
        
        # Results frame
        results_frame = ttk.Frame(main_frame)
        results_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.grid_rowconfigure(0, weight=1)  # Make notebook expandable
        results_frame.grid_columnconfigure(0, weight=1)
        
        # Create notebook for source and target results
        self.notebook = ttk.Notebook(results_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Source DB frame
        self.source_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.source_frame, text='Source Database')
        
        # Target DB frame
        self.target_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.target_frame, text='Target Database')
        
        # Create treeviews
        self.source_tree = self.create_treeview(self.source_frame)
        self.target_tree = self.create_treeview(self.target_frame)
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, pady=10)
        
        self.copy_button = ttk.Button(button_frame, text="Copy to Target", 
                                    command=self.copy_to_target, state='disabled')
        self.copy_button.grid(row=0, column=0, padx=5)
        
        ttk.Button(button_frame, text="Close", 
                  command=self.dialog.destroy).grid(row=0, column=1, padx=5)
        
    def create_treeview(self, parent):
        # Configure parent frame grid weights
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        
        tree = ttk.Treeview(parent, columns=('table', 'details'), show='headings')
        tree.heading('table', text='Table')
        tree.heading('details', text='Details')
        
        # Configure column widths
        tree.column('table', width=100, minwidth=100)
        tree.column('details', width=600, minwidth=300)
        
        # Add scrollbars
        vsb = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        hsb = ttk.Scrollbar(parent, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # Grid layout
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        vsb.grid(row=0, column=1, sticky=(tk.N, tk.S))
        hsb.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        return tree
    
    def search_item(self):
        search_term = self.kodeitem_var.get().strip()
        if not search_term:
            messagebox.showwarning("Warning", "Please enter a Kode Item or Barcode")
            return
        
        try:
            source_session = Sessions["source"]()
            target_session = Sessions["target"]()
            
            # Clear existing items
            for tree in [self.source_tree, self.target_tree]:
                for item in tree.get_children():
                    tree.delete(item)
            
            # First try to find kodeitem by barcode in source DB
            source_by_barcode = source_session.query(ItemSatuanJml).filter(
                ItemSatuanJml.kodebarcode == search_term
            ).first()
            
            # If found by barcode, use its kodeitem, otherwise use search_term as kodeitem
            kodeitem = source_by_barcode.kodeitem if source_by_barcode else search_term
            
            # Search in source DB
            source_item = source_session.query(Item).filter(Item.kodeitem == kodeitem).first()
            source_satuan = source_session.query(ItemSatuanJml).filter(
                ItemSatuanJml.kodeitem == kodeitem
            ).all()
            source_hj = source_session.query(ItemHargaJual).filter(
                ItemHargaJual.kodeitem == kodeitem
            ).all()
            
            # Search in target DB
            target_item = target_session.query(Item).filter(Item.kodeitem == kodeitem).first()
            target_satuan = target_session.query(ItemSatuanJml).filter(
                ItemSatuanJml.kodeitem == kodeitem
            ).all()
            target_hj = target_session.query(ItemHargaJual).filter(
                ItemHargaJual.kodeitem == kodeitem
            ).all()
            
            # Display source results
            if source_item:
                self.source_tree.insert('', 'end', values=('Item', 
                    f"Kode: {source_item.kodeitem}, Nama: {source_item.namaitem}"))
                for satuan in source_satuan:
                    barcode_info = f", Barcode: {satuan.kodebarcode}" if satuan.kodebarcode else ""
                    self.source_tree.insert('', 'end', values=('Satuan Jml', 
                        f"Satuan: {satuan.satuan}, Konv: {satuan.jumlahkonv}, " + 
                        f"Harga Pokok: {satuan.hargapokok}{barcode_info}"))
                for hj in source_hj:
                    self.source_tree.insert('', 'end', values=('Harga Jual', 
                        f"Satuan: {hj.satuan}, Tipe: {hj.tipehj}, Harga: {hj.hargajual}"))
            
            # Display target results
            if target_item:
                self.target_tree.insert('', 'end', values=('Item', 
                    f"Kode: {target_item.kodeitem}, Nama: {target_item.namaitem}"))
                for satuan in target_satuan:
                    barcode_info = f", Barcode: {satuan.kodebarcode}" if satuan.kodebarcode else ""
                    self.target_tree.insert('', 'end', values=('Satuan Jml', 
                        f"Satuan: {satuan.satuan}, Konv: {satuan.jumlahkonv}, " + 
                        f"Harga Pokok: {satuan.hargapokok}{barcode_info}"))
                for hj in target_hj:
                    self.target_tree.insert('', 'end', values=('Harga Jual', 
                        f"Satuan: {hj.satuan}, Tipe: {hj.tipehj}, Harga: {hj.hargajual}"))
            
            if not source_item:
                messagebox.showinfo("Not Found", "Item not found in source database")
            
            # Enable/disable copy button
            self.copy_button.config(state='normal' if source_item and not target_item else 'disabled')
            
        except Exception as e:
            error_msg = f"Error searching item: {str(e)}"
            logging.error(error_msg, exc_info=True)
            messagebox.showerror("Error", error_msg)
        finally:
            source_session.close()
            target_session.close()
    
    def copy_to_target(self):
        try:
            kodeitem = self.kodeitem_var.get().strip()
            source_session = Sessions["source"]()
            target_session = Sessions["target"]()
            
            # Get source data
            source_item = source_session.query(Item).filter(Item.kodeitem == kodeitem).first()
            source_satuan = source_session.query(ItemSatuanJml).filter(
                ItemSatuanJml.kodeitem == kodeitem).all()
            source_hj = source_session.query(ItemHargaJual).filter(
                ItemHargaJual.kodeitem == kodeitem).all()
            
            # Copy item
            new_item = Item(**{c.key: getattr(source_item, c.key) 
                             for c in Item.__table__.columns})
            target_session.add(new_item)
            
            # Copy satuan
            for satuan in source_satuan:
                new_satuan = ItemSatuanJml(**{c.key: getattr(satuan, c.key) 
                                            for c in ItemSatuanJml.__table__.columns})
                target_session.add(new_satuan)
            
            # Copy harga jual
            for hj in source_hj:
                new_hj = ItemHargaJual(**{c.key: getattr(hj, c.key) 
                                        for c in ItemHargaJual.__table__.columns})
                target_session.add(new_hj)
            
            target_session.commit()
            messagebox.showinfo("Success", "Item copied successfully to target database")
            
            # Refresh display
            self.search_item()
            
        except Exception as e:
            error_msg = f"Error copying item: {str(e)}"
            logging.error(error_msg, exc_info=True)
            messagebox.showerror("Error", error_msg)
            target_session.rollback()
        finally:
            source_session.close()
            target_session.close()

def show_add_item_dialog(parent):
    AddItemDialog(parent)
